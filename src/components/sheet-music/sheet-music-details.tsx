import { Badge, Box, Button, Center, createDisclosure, Divider, HStack, Icon, IconButton, Modal, ModalBody, ModalCloseButton, ModalContent, ModalHeader, ModalOverlay, Spinner, Tab, TabList, TabPanel, Tabs, Text, VStack } from "@hope-ui/solid";
import { useNavigate } from "@solidjs/router";
import { css } from "@stitches/core";
import abcjs from "abcjs";
import "abcjs/abcjs-audio.css";
import clsx from "clsx";
import { IconTypes } from "solid-icons";
import { FaRegularHeart, FaSolidClockRotateLeft, FaSolidCopy, FaSolidDownload, FaSolidEllipsisVertical, FaSolidEye, FaSolidFileAudio, FaSolidFlag, FaSolidHeart, FaSolidPause, FaSolidPenToSquare, FaSolidPlay, FaSolidStop, FaSolidThumbsDown, FaSolidThumbsUp, FaSolidTrash, FaSolidUpRightFromSquare, FaSolidX } from "solid-icons/fa";
import { Component, createEffect, createSignal, For, JSXElement, lazy, Match, onCleanup, onMount, ParentComponent, Show, Suspense, Switch } from "solid-js";
import { useService } from "solid-services";
import toast from "solid-toast";
import { ApiUserRecordProvider } from "~/contexts/user.context";
import { SheetMusicDetailResponse } from "~/models/sheet-music-dbo.models";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppVPSequencerFileLoad, AppVPSequencerTrack } from "~/proto/pianorhythm-app-renditions";
import { AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { Roles, rolesToJSON } from "~/proto/user-renditions";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import MidiPlayerService from "~/services/midi-player.service";
import NotificationService from "~/services/notification.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import { capitalizeFirstLetter } from "~/util/helpers";
import { logError } from "~/util/logger";
import MotionFadeIn from "../motion/motion.fade-in";
import UserProfileImage from "../user-profile-image";
import { ApiUserDto } from "~/types/user.types";
import { UsernameWithMiniProfileInContext, UsertagMiniProfileDefault } from "../username.w-miniprofile";
import { SynthAudioContext } from "~/util/synth-audio-ctx";
import AudioService from "~/services/audio.service";
import { MidiNoteSource } from "~/proto/midi-renditions";
import { MIDI } from "~/util/const.midi";
import AppThemesService from "~/services/app.themes.service";

const MidiLyricsDisplay = lazy(() => import("~/components/midi-player/midi-lyrics-display"));
const VPSheetSequencerControlsUI = lazy(() => import("~/components/midi-player/midi-vp-sequencer-ui"));

const decoder = new TextDecoder("utf-8");

const cardCSS = css({
  color: "white",
  ".card-detail--body": {
    "margin": "0 auto",
    "width": "100%",
    "position": "relative",
    "max-width": "100%",
    "min-height": "300px",
    "z-index": "1",

    ".detail-body--header": {
      "height": "35px",
      "width": "100%",
      "position": "sticky",
      "top": 0,
      "box-shadow": "0 1px 3px rgb(0 0 0 / 30%)",
      "z-index": "2",
      "background": "var(--hope-colors-primaryDark1)"
    },

    ".detail-body--output": {
      "height": "calc(100% - 80px)",
      "width": "100%",
      color: "black",
      "position": "relative",
      "padding": "10px",
      "background": "#efefef",
      "padding-left": "15px",
      "white-space": "break-spaces",
      "font-family": "'Verdana', 'Georgia', serif !important",
      "overflow-y": "scroll",
      "line-height": "2rem",
    },
    ".detail-body--footer": {
      "height": "30px",
      "padding": "0 6px",
      "position": "relative",
      "background": "var(--hope-colors-primaryDark1)"
    }
  },
  ".card-detail--content": {
    "margin": "0 auto",
    "padding": "2rem",
    "position": "relative",
    "display": "grid",
    "overflow": "hidden",
    "width": "100%",
    "max-width": "860px",
    "padding-bottom": "0",
    "justify-content": "center",
    "grid-gap": "1rem 2rem",
    "border-bottom-left-radius": "10px",
    "border-bottom-right-radius": "10px",
    "grid-template-columns": "minmax(0,35em) minmax(0, 250px)",
    "grid-template-rows": "minmax(0,auto) minmax(0,auto) 1fr minmax(0,auto)",
  },
  ".card-detail--content-meta": {
    "margin-top": "0 !important",
    "margin-bottom": "0 !important",
    "border-left": "2px solid $neutral10",
    "padding-left": "20px",

    ".content-meta-item": {
      "width": "100%",
    }
  }
});

const HeaderIcon: Component<{ onClick?: () => void, disabled?: boolean, color?: string, label: string, icon: IconTypes; }> = (props) => {
  return (<>
    <Box
      __tooltip_title={props.label}
      __tooltip_show_arrow={false}
      cursor={"pointer"}
      className={clsx([props.disabled && "disabled"])}
      transition="all 0.2s ease"
      color={props.color}
      _hover={{ color: "$accent1" }}
      onmousedown={() => { props.onClick?.(); }}
    >
      {props.icon({})}
    </Box>
  </>);
};

const ContentMetaItem: ParentComponent<{ header: string, value: string | number; }> = (props) => {
  return <>
    {props.value &&
      <Box className="content-meta-item">
        <Box color="$tertiary1">{props.header}</Box>
        <Box>{props.children || props.value}</Box>
      </Box>
    }
  </>;
};

type SheetMusicDetailsProps = {
  id: string;
  onClose?: () => void;
  modalSize?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "6xl" | "8xl" | "full";
  scrollBehavior?: "inside" | "outside";
  showHeader?: boolean;
  hideOptionsBar?: boolean;
  isolatedMode?: boolean;
  hideOpenInViewerIcon?: boolean;
};

interface TabItem {
  id: number;
  label: string;
}

interface SheetTabsProps {
  tabs: TabItem[];
  activeIndex: number;
  onTabChange: (index: number) => void;
}

const SheetTabs: Component<SheetTabsProps> = (props) => {
  return (
    <TabList>
      {props.tabs.map((tab) => (
        <Tab
          background={"$primary1"}
          color={props.activeIndex === tab.id ? "$accent1 !important" : "$neutral10 !important"}
          onMouseDown={() => props.onTabChange(tab.id)}
        >
          {tab.label}
        </Tab>
      ))}
    </TabList>
  );
};

const useTabState = (initialIndex: number = 0) => {
  const [tabIndex, setTabIndex] = createSignal(initialIndex);

  const handleTabChange = (index: number) => setTabIndex(index);

  return {
    tabIndex,
    handleTabChange
  };
};

const SHEET_TABS: TabItem[] = [
  { id: 0, label: 'Text' },
  { id: 1, label: 'Rendered' }
];

export const AbcNotationFormContent = (props: { rawEdit?: JSXElement, data: string; }) => {
  const { tabIndex, handleTabChange } = useTabState(0);

  const [playing, setPlaying] = createSignal(false);
  const audioService = useService(AudioService);
  const appThemeService = useService(AppThemesService);

  let abcFormContentElement!: HTMLDivElement;
  let synth: abcjs.MidiBuffer | undefined = undefined;
  let synthTimingCallbacks: abcjs.TimingCallbacks | undefined = undefined;

  createEffect(async () => {
    let rendered = abcjs.renderAbc(abcFormContentElement, props.data ?? "", {
      selectionColor: appThemeService().themeColors.accent,
    });

    if (abcjs.synth.supportsAudio()) {
      synth = new abcjs.synth.CreateSynth();

      await synth.init({
        audioContext: SynthAudioContext.Instance.context ?? new AudioContext(),
        visualObj: rendered[0],
        millisecondsPerMeasure: 500,
      });

      synthTimingCallbacks = new abcjs.TimingCallbacks(rendered[0], {
        qpm: rendered[0].getBpm() || 100,
        eventCallback: function (event: abcjs.NoteTimingEvent | null) {
          // Stopped playing
          if (playing() && event == null) {
            onStop();
            return;
          }

          event?.midiPitches?.forEach((note) => {
            audioService().parseMidiData(
              new Uint8Array([MIDI.NOTE_ON_BYTE, note.pitch, note.volume]),
              MIDI.MIDI_SYNTH_SOCKET_ID,
              MidiNoteSource.MIDI_PLAYER
            );
          });

          return "continue";
        }
      });
    }
  });

  const onResetSynth = () => {
    audioService().parseMidiData(
      new Uint8Array([MIDI.CONTROLLER_BYTE, 123, 0]),
      MIDI.MIDI_SYNTH_SOCKET_ID,
      MidiNoteSource.MIDI_PLAYER
    );
    audioService().resetChannelsToDefault();
  };

  const onStop = () => {
    synthTimingCallbacks?.stop();
    synthTimingCallbacks?.reset();
    onResetSynth();
    setPlaying(false);
  };

  onCleanup(() => {
    onResetSynth();
    synthTimingCallbacks?.stop();
    synth?.stop();
    synthTimingCallbacks = undefined;
    synth = undefined;
  });

  const UIControls = () => {
    return <HStack
      paddingTop={"$1"}
      paddingBottom={"$1"}
      justifyContent={"center"}
      id="abcnotation-sequencer-controls-header"
      spacing={"$2"}
    >
      <IconButton
        disabled={!playing()}
        __tooltip_title="Restart"
        onclick={() => {
          // Stop all notes
          audioService().parseMidiData(
            new Uint8Array([MIDI.CONTROLLER_BYTE, 123, 0]),
            MIDI.MIDI_SYNTH_SOCKET_ID,
            MidiNoteSource.MIDI_PLAYER
          );
          synthTimingCallbacks?.reset();
        }}
        size="xs" aria-label="Rewind (10s)" icon={<FaSolidClockRotateLeft />} />

      <Show
        when={!playing()}
        fallback={<IconButton
          __tooltip_title="Pause"
          onclick={() => { synthTimingCallbacks?.pause(); setPlaying(false); }}
          size="xs" aria-label="Pause" icon={<FaSolidPause />} />
        }
      >
        <IconButton
          __tooltip_title="Play"
          onclick={() => { synthTimingCallbacks?.start(); setPlaying(true); }}
          size="xs" aria-label="Play VP" icon={<FaSolidPlay />}
        />
      </Show>

      <IconButton
        disabled={!playing()}
        __tooltip_title="Stop"
        onclick={onStop}
        size="xs" aria-label="Stop" icon={<FaSolidStop />} />
    </HStack>;
  };

  return (<>
    <Tabs
      variant="cards"
      alignment="start"
    >
      <SheetTabs
        tabs={SHEET_TABS}
        activeIndex={tabIndex()}
        onTabChange={handleTabChange}
      />

      {tabIndex() == 0 &&
        <Show when={props.rawEdit != null} fallback={<Box>{props.data}</Box>}>
          {props.rawEdit}
        </Show>
      }
      <VStack display={tabIndex() == 1 ? "block" : "none"} spacing={"$2"} w="100%">
        <UIControls />
        <Divider />
        <Box w="100%" ref={abcFormContentElement} />
      </VStack>
    </Tabs>
  </>);
};

const SheetMusicDetails: Component<SheetMusicDetailsProps> = (props) => {
  const nav = useNavigate();
  const { isOpen, onOpen, onClose } = createDisclosure();
  const appService = useService(AppService);
  const midiPlayerService = useService(MidiPlayerService);
  const displayService = useService(DisplaysService);
  const sheetMusicService = useService(SheetMusicService);
  const [details, setDetails] = createSignal<SheetMusicDetailResponse>();
  const [text, setText] = createSignal<string>();
  const [title, setTitle] = createSignal<string>("Title");
  const [loading, setIsLoading] = createSignal(false);
  const [canDelete, setCanDelete] = createSignal(false);
  const [showLeftCol] = createSignal(true);
  const [canEdit, setCanEdit] = createSignal(false);
  const [canApprove, setCanApprove] = createSignal(false);
  const [isFavorited, setIsFavorited] = createSignal(false);
  const [errorMessage, setErrorMessage] = createSignal<string>();
  const [isCheckIsFavorited, setIsCheckIsFavorited] = createSignal(false);
  const [downloadingMidi, setDownloadingMidi] = createSignal(false);
  const [downloadMidiSub, setDownloadMidiSub] = createSignal<VoidFunction>();

  const DONWLOAD_MIDI_ID = "download-midi";
  const LOAD_SHEET_MUSIC_FAIL_ID = "load-sheet-music-fail";

  let checkIsFavoritedTimeout = -1;

  function closeModal() {
    props.onClose?.();
    onClose();

    if (!sheetMusicService().editMode()) {
      displayService().setDisplay("SHEET_MUSIC_DETAILS_MODAL", false);
      sheetMusicService().setActiveDetailID();
    }
  }

  const fetchIsFavorited = async () => {
    // let isFavorited = await sheetMusicService().sheetMusicIsFavorited(props.id, appService().client().usertag);
    setIsFavorited(false);
  };

  onMount(async () => {
    nav(`/sheet-music/${props.id}`);
    onOpen();
    setIsLoading(true);
    displayService().setDisplay("SHEET_MUSIC_DETAILS_MODAL", true);

    let output = await sheetMusicService().getSheetMusicDetails(props.id);
    let canApproveSheetMusic = appService().doesClientHaveSheetMusicFullAccess();

    if (output && output.data) {
      let domain = output.data;
      setDetails(output);

      try {
        let decodedString = decoder.decode(new Uint8Array(output.file.data as number[]).buffer);
        setText(decodedString);
      } catch (e) {
        NotificationService.show({
          id: LOAD_SHEET_MUSIC_FAIL_ID,
          title: "Data Parse Error",
          description: "There was an error decoding the file. Please check the console logs for more details.",
          type: "danger"
        });
        logError(`${e}`);
        return;
      }

      let canDoStuff = canApproveSheetMusic || appService().client().usertag == output.data.creatorUsername;
      setCanDelete(canDoStuff);
      setCanEdit(canDoStuff);
      setCanApprove(canApproveSheetMusic);
      setTitle(`Title: ${domain.title}`);
      await fetchIsFavorited();
    } else {
      if (output?.error || output?.error_description) {
        NotificationService.show({
          id: LOAD_SHEET_MUSIC_FAIL_ID,
          title: "Load Sheet Music Error",
          description: "There was an error loading the file. Please check the console logs for more details.",
          type: "danger"
        });
        logError(`${output.error_description}`);
        setErrorMessage(capitalizeFirstLetter(output.error_description));
      }

      setDetails(undefined);
      setText(undefined);
      setCanDelete(false);
      setCanApprove(false);
      setCanEdit(false);
      setTitle("Title");
    }
    setIsLoading(false);

    // TODO: Fix this
    setDownloadMidiSub(() => appService().appStateEffects.listen(async (effect) => {
      if (effect.action == AppStateEffects_Action.MidiSequencerDownloadMidi) {
        setDownloadingMidi(false);

        if (!effect.bytesPayload) {
          return NotificationService.show({
            id: DONWLOAD_MIDI_ID,
            closable: true,
            title: "Download Error",
            description: "Sorry, there was an error downloading the midi file.",
            type: "danger"
          });
        }

        // Trigger save file dialog for midi file
        let blob = new Blob([new Uint8Array(effect.bytesPayload.payload)], { type: "audio/midi" });
        let fileName = `${details()?.data?.title || "unknown_file"}.mid`;

        const onSuccess = () => {
          NotificationService.show({
            id: DONWLOAD_MIDI_ID,
            closable: true,
            title: "Download Complete",
            description: "Your download is complete!",
            type: "success"
          });
        };

        if ((window as any).showSaveFilePicker) {
          try {
            const opts = {
              types: [{
                description: 'Midi file',
                accept: { 'text/midi': ['.mid'] },
              }],
              suggestedName: fileName,
            };
            var handle = await (window as any).showSaveFilePicker(opts);
            var writable = await handle.createWritable();
            await writable.write(blob);
            writable.close();
            onSuccess();
            return;
          } catch (e) {
            console.error(e);
            return;
          }
        }

        let url = window.URL.createObjectURL(blob);
        let a = document.createElement("a");
        a.href = url;
        a.download = `${details()?.data?.title || "unknown_file"}.mid`;
        a.click();
        window.URL.revokeObjectURL(url);
        onSuccess();
      }
    }));
  });

  onCleanup(() => {
    nav(`/sheet-music`);
    window.clearTimeout(checkIsFavoritedTimeout);
    if (isVPSheet() && midiPlayerService().midiSequencerState.playerState > 1) {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.MidiSequencerStop,
      }), true);
    }
    downloadMidiSub()?.();
    NotificationService.hide(LOAD_SHEET_MUSIC_FAIL_ID);
  });

  const isVPSheet = () => details()?.data.category.toLowerCase() == "virtualpiano";
  const isAbcNotation = () => details()?.data.category.toLowerCase() == "abcmusicnotation";

  const getAppVPSequencerFileLoad = () => {
    return AppVPSequencerFileLoad.create({
      data: text()!,
      fileName: details()!.data.title,
      tracks: [
        AppVPSequencerTrack.create({
          index: 0,
          tempo: details()?.data?.bpm || midiPlayerService().DEFAULT_BPM,
        })
      ]
    });
  };

  const FavoritesIcon = () => {
    return <HeaderIcon
      icon={isFavorited() ? FaSolidHeart : FaRegularHeart}
      label={`${isFavorited() ? "Remove" : "Add"} this ${isFavorited() ? "from" : "to"} your favorites!`}
      color={isFavorited() ? "red" : undefined}
      onClick={() => {
        setIsCheckIsFavorited(true);

        if (isFavorited()) {
          sheetMusicService().sheetMusicRemoveFavorite(details()!.data.id, appService().client().usertag);
        } else {
          sheetMusicService().sheetMusicAddFavorite(details()!.data.id, appService().client().usertag);
        }

        window.clearTimeout(checkIsFavoritedTimeout);
        checkIsFavoritedTimeout = window.setTimeout(() => {
          fetchIsFavorited();
          setIsCheckIsFavorited(false);
          let msg = isFavorited() ? "un" : "";
          toast.success(`You've ${msg}favorited "${details()!.data.title}"!`, {
            duration: 3000
          });
        }, 500);

      }} />;
  };

  const CoreContent = () => {
    return (<>
      <MotionFadeIn
        style={{
          "width": "100%",
          "height": "100%",
        }}
      >
        <Switch>
          <Match when={loading()}>
            <Center h="100%">Loading...</Center>
          </Match>
          <Match when={errorMessage()}>
            <Center h="100%">
              <VStack spacing={"$2"}>
                <Text color="$danger10">Error: {errorMessage()}</Text>
                <Text fontSize={"$sm"} as="i" color="$neutral10">Check the console logs for more details.</Text>
                {/* Close Button */}
                <Box>
                  <Button size="sm" onClick={closeModal} variant={"outline"}>Close</Button>
                </Box>
              </VStack>
            </Center>
          </Match>
          <Match when={details() == null}>
            <Center h="100%" w="100%">
              <VStack spacing={"$2"}>
                <Box>No data found.</Box>
                {!props.showHeader && <Button size="sm" onClick={closeModal} variant={"outline"}>Close</Button>}
              </VStack>
            </Center>
          </Match>
          <Match when={details() != null}>
            <VStack class={cardCSS()}>
              <VStack
                className="card-detail--body"
                height={props.showHeader ? "60vh" : "80vh"}
              >
                {/* Header */}
                {!props.hideOptionsBar && <Box className="detail-body--header">
                  <HStack paddingLeft={15} paddingRight={5} h="100%" w="100%" justifyContent={"space-between"}>
                    <HStack spacing={"$2"}>
                      <HeaderIcon icon={FaSolidFlag} label={"Report this piece."} disabled />
                      {(details()?.data && canDelete()) && <HeaderIcon icon={FaSolidTrash} label={"Delete"} onClick={
                        () => sheetMusicService().deleteSheetMusicDialogue({
                          id: details()!.data.id,
                          title: details()!.data.title,
                          creatorUsername: details()!.data.creatorUsername,
                        }, closeModal)
                      } />}
                      {canEdit() &&
                        <HeaderIcon icon={FaSolidPenToSquare} label={"Edit"} onClick={
                          () => {
                            sheetMusicService().setEditMode(true);
                            sheetMusicService().setActiveViewerData(details()!);
                            displayService().setDisplay("SHEET_MUSIC_UPLOAD_MODAL", true);
                          }
                        } />
                      }
                      {(canApprove() && !details()?.data.approved) &&
                        <HeaderIcon icon={FaSolidThumbsUp} label={"Approve"} onClick={
                          () => sheetMusicService().startApprovalProcess(details()!.data, "approve", closeModal)
                        } />
                      }
                      {canApprove() &&
                        <HeaderIcon icon={FaSolidThumbsDown} label={"Disapprove"} onClick={
                          () => sheetMusicService().startApprovalProcess(details()!.data, "disapprove", closeModal)
                        } />
                      }
                    </HStack>

                    <HStack spacing={"$2"} h="100%" >
                      {appService().isClientMember() &&
                        <Show when={!isCheckIsFavorited()} fallback={<Spinner thickness="2px" />} >
                          <FavoritesIcon />
                        </Show>
                      }
                      {details()?.data && <>
                        <HeaderIcon icon={FaSolidCopy} label={"Copy Sheet"} onClick={
                          () => {
                            let sheetText = text();
                            if (!sheetText) return;
                            sheetMusicService().copySheetMusicTextToClipboard(sheetText);
                          }
                        } />

                        {!props.hideOpenInViewerIcon &&
                          <HeaderIcon icon={FaSolidUpRightFromSquare} label={"Open in Viewer"} onClick={
                            () => {
                              sheetMusicService().loadSheetMusic(details()!.data.id);
                              closeModal();
                              displayService().setDisplay("SHEET_MUSIC_REPO_MODAL", false);
                            }
                          } />
                        }
                      </>}

                      {isVPSheet() &&
                        <Show when={!downloadingMidi()} fallback={<Spinner thickness="2px" />}>
                          <HeaderIcon icon={FaSolidDownload} label={"Download as Midi"} onClick={
                            () => {
                              setDownloadingMidi(true);
                              NotificationService.show({
                                id: DONWLOAD_MIDI_ID,
                                title: "Downloading Midi",
                                description: "Your download will start shortly.",
                                type: "info"
                              });
                              appService().coreService()?.send_app_action(AppStateActions.create({
                                action: AppStateActions_Action.VPSequencerDownloadAsMidi,
                                vpFileLoad: getAppVPSequencerFileLoad()
                              }), true);
                            }
                          } />
                        </Show>
                      }

                      {!props.showHeader && <HeaderIcon color="$tertiary1" icon={FaSolidX} label={"Close"} onClick={closeModal} />}

                      <HeaderIcon icon={FaSolidEllipsisVertical} label={"More Options"} disabled />
                    </HStack>
                  </HStack>

                  {isVPSheet() &&
                    <Box background={"$primaryDark1"}>
                      <Divider marginBottom={5} opacity={0.4} />
                      <Suspense>
                        <VPSheetSequencerControlsUI input={getAppVPSequencerFileLoad()} />
                      </Suspense>
                    </Box>
                  }
                </Box>
                }

                {/* Body */}
                <Show when={props.isolatedMode}>
                  <Box
                    className="detail-body--output"
                  // height={"200% !important"}
                  >
                    <Text as="pre">{text()}</Text>
                  </Box>
                </Show>

                <Show when={!props.isolatedMode}>
                  <Box
                    className="detail-body--output"
                    marginTop={isVPSheet() ? 40 : 0}
                    textAlign={isVPSheet() ? "center" : "left"}
                  >
                    <Switch fallback={<Text as="pre">{text()}</Text>}>
                      <Match when={isVPSheet() && (midiPlayerService().midiSequencerState.lyrics ?? []).length > 0}>
                        <Box
                          overflowY={"scroll"}
                          color={"$neutral11 !important"}
                        >
                          <Suspense>
                            <MidiLyricsDisplay lyrics={midiPlayerService().midiSequencerState.lyrics ?? []} />
                          </Suspense>
                        </Box>
                      </Match>
                      <Match when={isAbcNotation()}>
                        <AbcNotationFormContent data={text()!} />
                      </Match>
                    </Switch>
                  </Box>
                </Show>

                {/* Footer */}
                {/* <Box className="detail-body--footer"> </Box> */}
              </VStack>

              {/* Content */}
              <HStack
                // marginTop={isVPSheet() ? 0 : -30}
                padding={15} w="100%"
                justifyContent={"space-between"} alignItems="flex-start"
              >
                {showLeftCol() &&
                  <VStack spacing={"$3"} alignItems={"flex-start"} justifyContent="flex-start" w="100%" paddingRight={10}>
                    {/* Title */}
                    {details()?.data.title &&
                      <Box fontWeight={"$bold"} fontSize={"$3xl"}>
                        {details()!.data.title}
                      </Box>
                    }

                    {/* Creator */}
                    {details()?.data.creatorUsername &&
                      <HStack spacing={"$2"}>
                        <ApiUserRecordProvider value={{
                          default: {
                            ...ApiUserDto.DefaultWithUsername2(details()!.data.creatorUsername),
                            roles: [rolesToJSON(Roles.MEMBER)],
                          }
                        }}>
                          <UserProfileImage width="40px" customBorder="solid 2px $neutral11" />
                          <UsernameWithMiniProfileInContext textColor="$neutral11" />
                        </ApiUserRecordProvider>
                      </HStack>
                    }

                    <Divider />

                    {/* Views & Favorites */}
                    <HStack spacing={"$3"} color={"$neutral11"} w="100%" >
                      <HStack __tooltip_title="Views" spacing={"$1"}><FaSolidEye font-size="14px" /> {details()?.data.views ?? 0}</HStack>
                      <HStack __tooltip_title="Favorites" spacing={"$1"}>
                        <Show when={!isCheckIsFavorited()} fallback={<Spinner thickness="2px" />} >
                          <FavoritesIcon />
                        </Show>
                        {details()?.data.favorites || 0}</HStack>
                    </HStack>

                    {/* Tempo */}
                    {details()?.data.bpm &&
                      <>
                        <Badge cursor="pointer" _hover={{ "color": "$accent1" }}>
                          Tempo: {details()?.data.bpm}
                        </Badge>
                      </>
                    }

                    {(details()?.data.tags || []).length > 0 && <>
                      <Divider />
                      {/* Tags */}
                      <Box className="card-detail--content-tags" w="100%">
                        <HStack spacing={"$1"} justifyContent="flex-start" w="100%">
                          <For each={details()?.data.tags || []}>
                            {(tag) =>
                              <Badge
                                __tooltip_title={`Search for sheets by this tag`}
                                onclick={() => {
                                  closeModal();
                                  sheetMusicService().searchRepoByFilter("tags", tag);
                                }}
                                cursor="pointer" _hover={{ "color": "$accent1" }}>
                                {tag}
                              </Badge>
                            }
                          </For>
                        </HStack>
                      </Box>
                    </>}

                    {/* Description */}
                    {details()?.data.description &&
                      <>
                        <Divider />
                        {/* Description */}
                        <Box fontWeight={"bold"}>Description</Box>
                        <Box userSelect="text" w="100%" style={{ "white-space": "pre-line" }}>
                          {details()!.data.description}
                        </Box>
                      </>}
                  </VStack>
                }

                <VStack
                  className="card-detail--content-meta" spacing={"$4"}
                  width={"50%"}
                  height={"100%"}
                  overflowY={"scroll"}
                >
                  {canApprove() && <ContentMetaItem header={"ID"} value={details()?.data.id.toUpperCase() || "N/A"} />}
                  <ContentMetaItem header={"Uploaded On"} value={(new Date(details()?.data.createdDate || "")).toLocaleDateString()} />
                  <ContentMetaItem header={"Category"} value={details()?.data.category || "N/A"} />
                  <ContentMetaItem header={"Difficulty"} value={details()?.data.difficultyLevel || "N/A"} />
                  <ContentMetaItem header={"Original Artist"} value={details()?.data.songArtist || "N/A"} />
                  <ContentMetaItem header={"Artist's Album"} value={details()?.data.songAlbum || "N/A"} />
                  <ContentMetaItem header={"Tempo"} value={details()?.data.bpm || "N/A"} />
                  {canApprove() && <ContentMetaItem header={"Privacy"} value={details()?.data.privacyStatus || "N/A"} />}

                  {details()?.data.createdDate && <ContentMetaItem header={"Last Edited"} value={(new Date(details()?.data.createdDate || "N/A")).toLocaleDateString()} />}
                  {details()?.data.lastEditedBy &&
                    <ContentMetaItem header={"Last Edited By"} value={details()?.data.lastEditedBy || "N/A"}>
                      <UsertagMiniProfileDefault isMember usertag={details()?.data.lastEditedBy || "N/A"} />
                    </ContentMetaItem>
                  }

                  {details()?.data.approved &&
                    <>
                      <ContentMetaItem header={"Last Approved By"} value={details()?.data.lastApprovedBy || "N/A"}>
                        <UsertagMiniProfileDefault isMember usertag={details()?.data.lastApprovedBy || "N/A"} />
                      </ContentMetaItem>
                      {details()?.data.lastApprovedDate && <ContentMetaItem header={"Last Approved Date"} value={details()?.data.lastApprovedDate || "N/A"} />}
                    </>
                  }

                  {!details()?.data.approved &&
                    <>
                      <ContentMetaItem header={"Last Disapproved By"} value={details()?.data.lastDisapprovedBy || "N/A"}>
                        <UsertagMiniProfileDefault isMember usertag={details()?.data.lastDisapprovedBy || "N/A"} />
                      </ContentMetaItem>
                      {details()?.data.lastDisapprovedDate && <ContentMetaItem header={"Last Disapproved Date"} value={details()?.data.lastDisapprovedDate || "N/A"} />}
                      <ContentMetaItem header={"Disapproval Reason"} value={details()?.data.disapprovalReason || "N/A"} />
                    </>
                  }
                </VStack>
              </HStack>
            </VStack>
          </Match>
        </Switch >
      </MotionFadeIn >
    </>);
  };

  return (<>
    <Show when={props.isolatedMode}>
      <Box w="100%" h="100%"
        overflow={"scroll"}
        position={"absolute"}
        margin={"0 auto"}
        top={0}
        bottom={0}
        left={0}
        right={0}
        zIndex={1000}
        backgroundColor={"$primaryDarkAlpha2"}
      >
        <Center w="100%">
          <CoreContent />
        </Center>
      </Box>
    </Show>

    {!props.isolatedMode && <Modal
      closeOnOverlayClick={false}
      centered
      opened={isOpen()}
      onClose={closeModal}
      scrollBehavior={props.scrollBehavior ?? "outside"}
      size={props.modalSize ?? "4xl"}
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent
        zIndex={"calc(var(--hope-zIndices-modal) + 1)"}
        h="100%"
        w="100%"
        maxHeight={props.showHeader ? "calc(100% - 7.5rem)" : "100% !important"}
      >
        {props.showHeader &&
          <>
            <ModalCloseButton onClick={closeModal} />
            <ModalHeader><Icon as={FaSolidFileAudio} />{title()}</ModalHeader>
          </>
        }

        <ModalBody w="100%" padding={0}>
          <CoreContent />
        </ModalBody>
      </ModalContent>
    </Modal>
    }
  </>);
};

export default SheetMusicDetails;