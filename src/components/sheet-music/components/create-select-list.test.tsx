import { render, screen, fireEvent } from '@solidjs/testing-library';
import { describe, it, expect, vi } from 'vitest';
import { HopeProvider } from '@hope-ui/solid';
import { CreateSelectList, SelectListItem } from './create-select-list';
import ThemeConfig from '~/util/theme-config';

const TestWrapper = (props: any) => (
  <HopeProvider config={ThemeConfig}>
    {props.children}
  </HopeProvider>
);

describe('CreateSelectList', () => {
  const mockData: SelectListItem[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' }
  ];

  it('should render with placeholder', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
        />
      </TestWrapper>
    ));

    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('should render with default value', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          defaultValue="option2"
        />
      </TestWrapper>
    ));

    // The selected value should be displayed
    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('should call onSetValue when selection changes', async () => {
    const onSetValue = vi.fn();
    
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Click to open the select
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);

    // Wait for options to appear and click one
    await vi.waitFor(() => {
      const option = screen.getByText('option1');
      fireEvent.click(option);
    });

    expect(onSetValue).toHaveBeenCalledWith('option1');
  });

  it('should handle multiple selection', () => {
    const onSetValue = vi.fn();
    
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select options"
          isMultiple={true}
          defaultValue={['option1', 'option2']}
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Both selected values should be displayed
    expect(screen.getByText('option1')).toBeInTheDocument();
    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('should show invalid state when invalid prop is true', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          invalid={true}
        />
      </TestWrapper>
    ));

    // The select should have invalid styling (this depends on Hope UI implementation)
    const select = screen.getByRole('button');
    expect(select).toHaveAttribute('aria-invalid', 'true');
  });

  it('should display tooltips for options with labels', async () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          defaultValue="option1"
        />
      </TestWrapper>
    ));

    // The tooltip should be set on the selected option
    const selectedOption = screen.getByText('option1');
    expect(selectedOption.parentElement).toHaveAttribute('title', 'Option 1');
  });

  it('should allow removing items in multiple selection mode', async () => {
    const onSetValue = vi.fn();
    
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select options"
          isMultiple={true}
          defaultValue={['option1', 'option2']}
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Find and click the remove button for option1
    const removeButtons = screen.getAllByRole('button');
    const removeButton = removeButtons.find(button => 
      button.querySelector('svg') // The X icon
    );
    
    if (removeButton) {
      fireEvent.click(removeButton);
      expect(onSetValue).toHaveBeenCalledWith(['option2']);
    }
  });

  it('should update value when valueSignalChange prop changes', () => {
    let signalValue = 'option1';
    const valueSignalChange = () => signalValue;
    
    const { rerender } = render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          valueSignalChange={valueSignalChange}
        />
      </TestWrapper>
    ));

    expect(screen.getByText('option1')).toBeInTheDocument();

    // Change the signal value and rerender
    signalValue = 'option2';
    rerender();

    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('should render all options in the dropdown', async () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
        />
      </TestWrapper>
    ));

    // Click to open the select
    const trigger = screen.getByRole('button');
    fireEvent.click(trigger);

    // All options should be available
    await vi.waitFor(() => {
      expect(screen.getByText('option1')).toBeInTheDocument();
      expect(screen.getByText('option2')).toBeInTheDocument();
      expect(screen.getByText('option3')).toBeInTheDocument();
    });
  });

  it('should handle empty data array', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={[]}
          placeholder="No options available"
        />
      </TestWrapper>
    ));

    expect(screen.getByText('No options available')).toBeInTheDocument();
  });

  it('should initialize with empty array for multiple selection', () => {
    const onSetValue = vi.fn();
    
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select options"
          isMultiple={true}
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Should start with empty selection
    expect(screen.getByText('Select options')).toBeInTheDocument();
  });
});
