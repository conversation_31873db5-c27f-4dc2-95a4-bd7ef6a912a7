import { SearchDriverOptions } from "@elastic/search-ui";
import { MyAPIConnector } from "~/components/search-ui/search-ui.types";

/**
 * Base search configuration for sheet music
 */
export const BASE_SEARCH_CONFIG: SearchDriverOptions = {
  debug: false,
  trackUrlState: false,
  alwaysSearchOnInitialLoad: true,
  apiConnector: new MyAPIConnector("/api/v1/sheet_music/searchDriver"),
  searchQuery: {
    search_fields: {
      "title": {},
      "songAlbum": {},
      "uuid": {},
      "creatorUsername": {},
    },
    facets: {
      "category": { type: "value" },
      "difficulty": { type: "value" },
      "tags": { type: "value" },
    }
  }
};

/**
 * Available results per page options
 */
export const RESULTS_PER_PAGE_OPTIONS = [9, 18, 36] as const;

/**
 * Default results per page
 */
export const DEFAULT_RESULTS_PER_PAGE = RESULTS_PER_PAGE_OPTIONS[0];

/**
 * Local storage key for results per page preference
 */
export const STORAGE_KEY_RESULTS_PER_PAGE = "sheetMusicRepoResultsPerPage";
