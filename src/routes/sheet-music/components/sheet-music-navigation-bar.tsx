import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Image } from "@hope-ui/solid";
import { createAsync, useNavigate } from "@solidjs/router";
import { lazy, Suspense } from "solid-js";
import { useService } from "solid-services";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import ResourceService from "~/services/resource.service";
import { IMAGES } from "~/util/const.common";

const LoginNavbarProfile = lazy(() => import("~/components/login/login-navbar-profile"));

interface SheetMusicNavigationBarProps {
  // Could add props for customization if needed
}

/**
 * Navigation bar component for the sheet music page
 */
export function SheetMusicNavigationBar(props: SheetMusicNavigationBarProps) {
  const appService = useService(AppService);
  const displaysService = useService(DisplaysService);
  const resourceService = useService(ResourceService);
  const navigate = useNavigate();
  
  const logo = createAsync(() => resourceService().getAssetImage(IMAGES.LOGO));

  const handleHomeClick = (e: Event) => {
    e.preventDefault();
    navigate("/", { resolve: true });
  };

  const handleUploadClick = () => {
    displaysService().setDisplay("SHEET_MUSIC_UPLOAD_MODAL", true);
  };

  return (
    <HStack
      p="$2" 
      w="100%"
      justifyContent="flex-end" 
      position="relative"
      borderBottom="1px solid $neutral11" 
      mb={7}
      fontSize="0.85em"
      textTransform="lowercase"
    >
      <HStack 
        spacing="$2" 
        justifySelf="flex-end" 
        position="absolute" 
        left={20}
      >
        {logo() && (
          <Image
            loading="lazy"
            w="50px"
            alt="PianoRhythm Logo"
            borderRadius="50%"
            src={logo()}
          />
        )}
        
        <Anchor
          color="$neutral11"
          href="."
          onClick={handleHomeClick}
          className="nav-link"
        >
          Home
        </Anchor>
        
        <Anchor
          color="$neutral11"
          href="/login"
          className="nav-link"
        >
          Login
        </Anchor>
        
        <Anchor
          color="$neutral11"
          className="nav-link"
          href="/register"
        >
          Register
        </Anchor>
        
        {appService().isClientMember() && (
          <Button
            __tooltip_title="Upload your own sheet music"
            onClick={handleUploadClick}
            size="sm"
          >
            Upload
          </Button>
        )}
      </HStack>
      
      <Suspense>
        <LoginNavbarProfile />
      </Suspense>
    </HStack>
  );
}
