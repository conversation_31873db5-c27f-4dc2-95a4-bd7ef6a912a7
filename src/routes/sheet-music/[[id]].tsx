import { Box, Center, HStack, VStack } from "@hope-ui/solid";
import { Title } from "@solidjs/meta";
import { createAsync, useParams } from "@solidjs/router";
import { FaSolidMusic } from "solid-icons/fa";
import { createSignal, lazy, onCleanup, onMount, Show, Suspense } from "solid-js";
import { useService } from "solid-services";
import { getMemberSessionInfo } from "~/lib";
import { SheetMusicDto } from "~/models/sheet-music-dbo.models";
import DisplaysService from "~/services/displays.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import {
  useSheetMusicInitialization,
  useSheetMusicSearch,
  useUserAuthentication,
  SheetMusicNavigationBar,
  SheetMusicSearchView,
  SheetMusicErrorBoundary
} from "./index";

const SheetMusicDetails = lazy(() => import("~/components/sheet-music/sheet-music-details"));
const SidebarDocsPanel = lazy(() => import("~/components/modals/sidebar-docs-panel"));
const SheetMusicUploadModal = lazy(() => import("~/components/sheet-music/sheet-music-upload"));

export default function MainPage() {
  const params = useParams();
  const displayService = useService(DisplaysService);
  const sheetMusicService = useService(SheetMusicService);

  // Custom hooks for separated concerns
  useSheetMusicInitialization();
  const { user } = useUserAuthentication();
  const { driverOptions, defaultResultsPerPage, saveResultsPerPage, RESULTS_PER_PAGE } = useSheetMusicSearch();

  // Local state
  const [selectedSheets, setSelectedSheets] = createSignal<SheetMusicDto[]>([]);
  const [activeDetailID, setActiveDetailID] = createSignal<string>();

  // Handle initial sheet music loading from URL parameter
  const initializeSheetFromParams = async () => {
    if (params.id) {
      try {
        const { getSheetMusicQuery } = await import("~/lib/sheet_music");
        const sheet = await getSheetMusicQuery(decodeURI(params.id!));
        if (sheet) {
          setActiveDetailID(sheet.id);
          sheetMusicService().setActiveDetailID(sheet.id);
        }
      } catch (error) {
        console.error("Failed to load sheet music from URL:", error);
      }
    }
  };

  // Initialize sheet music from URL params on mount
  onMount(() => {
    initializeSheetFromParams();
  });

  // Event handlers
  const handleActiveDetailChange = (id: string) => {
    setActiveDetailID(id);
  };

  const handleDriverStateChange = (state: any) => {
    if (state.resultsPerPage) {
      saveResultsPerPage(state.resultsPerPage);
    }
  };

  return (
    <SheetMusicErrorBoundary>
      <Box
        h="100%"
        w="100%"
        overflow="scroll"
        background="$primaryDark1"
      >
        <Title>PianoRhythm - Sheet Music</Title>
        <SheetMusicNavigationBar />

        {displayService().getDisplay("SIDEBAR_HELP_DOCS") && (
          <Suspense>
            <SidebarDocsPanel />
          </Suspense>
        )}

        <Show when={activeDetailID()}>
          <Suspense>
            <SheetMusicDetails
              id={activeDetailID() as string}
              onClose={() => setActiveDetailID(undefined)}
              showHeader={false}
              hideOpenInViewerIcon={true}
              scrollBehavior="inside"
            />
          </Suspense>
        </Show>

        <Center w="90%" justifyContent="center" m="auto">
          <VStack w="100%">
            <HStack w="100%" p="$2" spacing="$2" background="$primaryDarkAlpha">
              <FaSolidMusic size={14} />
              <Box>Sheet Music Repo</Box>
            </HStack>

            <SheetMusicSearchView
              config={driverOptions()}
              initialSearchTerm={params.id ? decodeURI(params.id!) : undefined}
              defaultResultsPerPage={defaultResultsPerPage()}
              resultsPerPageOptions={RESULTS_PER_PAGE}
              onSelectedSheetsChange={setSelectedSheets}
              onActiveDetailChange={handleActiveDetailChange}
              onDriverStateChange={handleDriverStateChange}
            />
          </VStack>
        </Center>

        {displayService().getDisplay("SHEET_MUSIC_UPLOAD_MODAL") && (
          <Suspense>
            <SheetMusicUploadModal />
          </Suspense>
        )}
      </Box>
    </SheetMusicErrorBoundary>
  );
}