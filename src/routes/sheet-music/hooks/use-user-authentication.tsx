import { createAsync, useAction } from "@solidjs/router";
import { createReaction } from "solid-js";
import { useService } from "solid-services";
import { getMemberSessionInfo, login } from "~/lib";
import { rolesFromJSON, UserClientDto, UserDto } from "~/proto/user-renditions";
import AppService from "~/services/app.service";
import LoginService from "~/services/login.service";
import { createLoginFormData } from "~/util/helpers";

/**
 * Custom hook for handling user authentication and session management
 */
export function useUserAuthentication() {
  const appService = useService(AppService);
  const loginService = useService(LoginService);
  
  const user = createAsync(() => getMemberSessionInfo(), {
    deferStream: true,
    initialValue: { username: "", usertag: "", roles: [] }
  });

  const loginAction = useAction(login);

  const handleUserSession = async (targetUser: any) => {
    if (!targetUser) {
      // Attempt auto-login if no user session
      const sessionInfo = await loginService().onLogin(
        loginAction(createLoginFormData("", undefined, true))
      );
      if (!sessionInfo) return;
      targetUser = sessionInfo;
    }

    if (!targetUser) return;

    // Update login service state
    loginService().setCurrentLoggedInUsername({
      usertag: targetUser.usertag,
      username: targetUser.username,
    });
    loginService().setLoggedIn(true);
    loginService().onUserAutoLoginNotification(targetUser.username);

    // Update app service client state
    appService().clearClient();
    appService().onClientLoaded(UserClientDto.create({
      userDto: UserDto.create({
        usertag: targetUser.usertag,
        username: targetUser.username,
        roles: targetUser.roles.map(rolesFromJSON),
      }),
    }));
  };

  // React to user changes
  const onGetMember = createReaction(handleUserSession);
  onGetMember(user);

  return {
    user,
    isAuthenticated: () => !!user()?.username,
    hasFullAccess: () => appService().doesClientHaveSheetMusicFullAccess(),
    isMember: () => appService().isClientMember(),
  };
}
